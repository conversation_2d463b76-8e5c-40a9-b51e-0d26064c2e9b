#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证 miniature_ratio 完全移除的测试脚本
"""

import os
import sys

def check_files_for_miniature_ratio():
    """检查关键文件中是否还有 miniature_ratio 引用"""
    
    # 需要检查的关键文件
    key_files = [
        'utils/config_manager_duckdb.py',
        'utils/image_processor.py',
        'ui/layout_worker.py',
        'ui/rectpack_layout_worker.py',
        'core/rectpack_arranger.py',
        'core/rectpack_test_mode.py',
        'core/rectpack_documentation_generator.py',
        'robot_ps_smart_app.py'
    ]
    
    print("🔍 检查关键文件中的 miniature_ratio 引用...")
    print("=" * 60)
    
    found_references = []
    
    for file_path in key_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                lines = content.split('\n')
                for line_num, line in enumerate(lines, 1):
                    if 'miniature_ratio' in line.lower():
                        # 排除注释中的说明
                        if not (line.strip().startswith('#') and ('移除' in line or '已移除' in line or '不再使用' in line)):
                            found_references.append({
                                'file': file_path,
                                'line': line_num,
                                'content': line.strip()
                            })
                            
                print(f"✅ {file_path} - 已检查")
                
            except Exception as e:
                print(f"❌ {file_path} - 检查失败: {e}")
        else:
            print(f"⚠️ {file_path} - 文件不存在")
    
    print("\n" + "=" * 60)
    
    if found_references:
        print(f"❌ 发现 {len(found_references)} 个 miniature_ratio 引用:")
        for ref in found_references:
            print(f"   📁 {ref['file']}:{ref['line']}")
            print(f"      {ref['content']}")
        return False
    else:
        print("✅ 所有关键文件中已完全移除 miniature_ratio 引用!")
        return True

def test_config_manager():
    """测试配置管理器是否正确移除了 miniature_ratio"""
    print("\n🧪 测试配置管理器...")
    
    try:
        from utils.config_manager_duckdb import ConfigManagerDuckDB
        
        config_manager = ConfigManagerDuckDB()
        test_settings = config_manager.get_test_mode_settings()
        
        print(f"测试模式设置: {test_settings}")
        
        if 'miniature_ratio' in test_settings:
            print("❌ 配置管理器仍包含 miniature_ratio")
            return False
        else:
            print("✅ 配置管理器已正确移除 miniature_ratio")
            
        # 检查必要的设置是否存在
        required_keys = ['is_test_mode', 'is_test_all_data']
        missing_keys = [key for key in required_keys if key not in test_settings]
        
        if missing_keys:
            print(f"❌ 缺少必要的配置项: {missing_keys}")
            return False
        else:
            print("✅ 所有必要的配置项都存在")
            
        config_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False

def test_rectpack_test_mode():
    """测试 RectPack 测试模式是否正确工作"""
    print("\n🧪 测试 RectPack 测试模式...")
    
    try:
        from core.rectpack_test_mode import convert_pattern_items_to_px_data, create_container_config
        
        # 测试数据转换
        pattern_items = [
            {'width_cm': 120, 'height_cm': 60, 'pattern_name': '测试图案1', 'quantity': 1}
        ]
        
        px_data = convert_pattern_items_to_px_data(pattern_items)
        
        if len(px_data) == 1 and px_data[0][0] == 120 and px_data[0][1] == 60:
            print("✅ 数据转换正常 (cm直接转px)")
        else:
            print(f"❌ 数据转换异常: {px_data}")
            return False
            
        # 测试容器配置
        container_config = create_container_config(
            canvas_width_cm=200,
            horizontal_expansion_cm=2,
            max_height_cm=5000,
            image_spacing_cm=0.1
        )
        
        if container_config['actual_width'] == 202:  # 200 + 2
            print("✅ 容器配置正常 (cm直接转px)")
        else:
            print(f"❌ 容器配置异常: {container_config}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ RectPack测试模式测试失败: {e}")
        return False

def test_image_processor():
    """测试图片处理器是否正确移除了 miniature_ratio"""
    print("\n🧪 测试图片处理器...")
    
    try:
        from utils.image_processor import get_image_processor
        
        # 测试模式配置（不包含 miniature_ratio）
        test_config = {
            'is_test_all_data': False
        }
        
        processor = get_image_processor(is_test_mode=True, config=test_config)
        
        if hasattr(processor, 'miniature_ratio'):
            print("❌ 图片处理器仍包含 miniature_ratio 属性")
            return False
        else:
            print("✅ 图片处理器已正确移除 miniature_ratio 属性")
            
        return True
        
    except Exception as e:
        print(f"❌ 图片处理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始验证 miniature_ratio 完全移除")
    print("目标: 确保所有 miniature_ratio 缩小模型逻辑已完全移除")
    print("新方式: 测试模式使用 cm 直接转 px 的方式实现缩小模型")
    
    tests = [
        ("文件内容检查", check_files_for_miniature_ratio),
        ("配置管理器", test_config_manager),
        ("RectPack测试模式", test_rectpack_test_mode),
        ("图片处理器", test_image_processor),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("🎯 验证结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 验证成功！")
        print("✅ 所有 miniature_ratio 缩小模型逻辑已完全移除")
        print("✅ 测试模式现在使用 cm 直接转 px 的方式实现缩小模型")
        print("✅ 系统功能正常，符合用户要求")
        return True
    else:
        print("\n⚠️ 验证失败，仍有部分 miniature_ratio 引用需要清理")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
