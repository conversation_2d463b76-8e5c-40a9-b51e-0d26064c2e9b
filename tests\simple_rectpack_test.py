#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化的RectPack测试模式验证脚本
验证核心功能是否正常工作
"""

import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """测试导入功能"""
    try:
        from core.rectpack_test_mode import (
            convert_pattern_items_to_px_data,
            get_container_config_px,
            validate_px_test_data,
            convert_to_image_objects
        )
        print("✓ 核心模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ 核心模块导入失败: {e}")
        return False

def test_data_conversion():
    """测试数据转换功能"""
    try:
        from core.rectpack_test_mode import convert_pattern_items_to_px_data

        # 测试数据
        pattern_items = [
            {'width_cm': 100, 'height_cm': 80, 'pattern_name': '测试图案1', 'quantity': 1},
            {'width_cm': 120, 'height_cm': 60, 'pattern_name': '测试图案2', 'quantity': 2},
        ]

        # 转换数据
        px_data = convert_pattern_items_to_px_data(pattern_items)

        if len(px_data) == 3:  # 1 + 2 = 3张图片
            print("✓ 数据转换功能正常")
            print(f"  转换结果: {px_data}")
            return True
        else:
            print(f"✗ 数据转换结果错误: 期望3张图片，实际{len(px_data)}张")
            return False

    except Exception as e:
        print(f"✗ 数据转换测试失败: {e}")
        return False

def test_container_config():
    """测试容器配置功能"""
    try:
        from core.rectpack_test_mode import get_container_config_px

        config = get_container_config_px(
            canvas_width_cm=200,
            horizontal_expansion_cm=2,
            max_height_cm=500,
            image_spacing_cm=1
        )

        expected_actual_width = 200 + 2  # 基础宽度 + 水平拓展
        if config['actual_width'] == expected_actual_width:
            print("✓ 容器配置功能正常")
            print(f"  配置结果: {config}")
            return True
        else:
            print(f"✗ 容器配置错误: 期望宽度{expected_actual_width}，实际{config['actual_width']}")
            return False

    except Exception as e:
        print(f"✗ 容器配置测试失败: {e}")
        return False

def test_rectpack_availability():
    """测试RectPack库可用性"""
    try:
        from core.rectpack_test_mode import create_rectpack_config

        config = create_rectpack_config()
        if config['available']:
            print("✓ RectPack库可用")
            return True
        else:
            print("⚠️ RectPack库不可用，将使用简化算法")
            return True  # 这不是错误，只是警告

    except Exception as e:
        print(f"✗ RectPack库测试失败: {e}")
        return False

def test_basic_workflow():
    """测试基本工作流程"""
    try:
        from core.rectpack_test_mode import (
            convert_pattern_items_to_px_data,
            get_container_config_px,
            validate_px_test_data,
            convert_to_image_objects,
            create_rectpack_config
        )

        print("开始基本工作流程测试...")

        # 1. 准备测试数据
        pattern_items = [
            {'width_cm': 80, 'height_cm': 60, 'pattern_name': '小图案', 'quantity': 1},
            {'width_cm': 100, 'height_cm': 80, 'pattern_name': '中图案', 'quantity': 1},
        ]

        # 2. 转换为px数据
        px_data = convert_pattern_items_to_px_data(pattern_items, miniature_ratio=1.0)
        print(f"  ✓ 转换px数据: {len(px_data)}张图片")

        # 3. 验证数据
        if not validate_px_test_data(px_data):
            print("  ✗ 数据验证失败")
            return False
        print("  ✓ 数据验证通过")

        # 4. 转换为图片对象
        image_objects = convert_to_image_objects(px_data)
        print(f"  ✓ 转换图片对象: {len(image_objects)}个")

        # 5. 创建容器配置
        container_config = get_container_config_px(
            canvas_width_cm=200,
            horizontal_expansion_cm=2,
            max_height_cm=500,
            miniature_ratio=1.0
        )
        print(f"  ✓ 容器配置: {container_config['actual_width']}x{container_config['max_height']}px")

        # 6. 检查RectPack配置
        rectpack_config = create_rectpack_config()
        print(f"  ✓ RectPack配置: {'可用' if rectpack_config['available'] else '不可用'}")

        print("✓ 基本工作流程测试通过")
        return True

    except Exception as e:
        print(f"✗ 基本工作流程测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("RectPack测试模式简化验证")
    print("=" * 50)

    tests = [
        ("模块导入", test_import),
        ("数据转换", test_data_conversion),
        ("容器配置", test_container_config),
        ("RectPack库", test_rectpack_availability),
        ("基本工作流程", test_basic_workflow),
    ]

    results = []
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 30)
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))

    # 显示总结
    print("\n" + "="*50)
    print("测试总结")
    print("="*50)

    passed = sum(1 for _, success in results if success)
    total = len(results)

    for test_name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name:<15} {status}")

    print("-" * 30)
    print(f"总计: {passed}/{total} 个测试通过")

    if passed == total:
        print("\n🎉 所有基础测试通过！")
        print("RectPack测试模式核心功能正常")
    else:
        print(f"\n⚠️ {total - passed} 个测试失败")
        print("需要检查相关功能")

if __name__ == "__main__":
    main()
