#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置管理模块 - DuckDB 实现

提供基于 DuckDB 的应用配置管理功能：
1. 使用 DuckDB 数据库存储配置
2. 管理默认设置
3. 保存用户偏好
4. 从 Supabase 同步配置
"""

import os
import logging
import duckdb
from typing import Dict, Any, Optional, Tuple
from PyQt6.QtWidgets import QMessageBox
from .supabase_helper import SupabaseHelper

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("ConfigManager")

class ConfigManagerDuckDB:
    """配置管理类 - DuckDB 实现"""

    def __init__(self, db_file: str = "config.db", supabase_helper=None):
        """初始化配置管理器

        Args:
            db_file: 配置数据库文件路径
            supabase_helper: Supabase辅助类实例，如果为None则创建新实例
        """
        self.db_file = db_file
        self.supabase_helper = supabase_helper if supabase_helper else SupabaseHelper()
        self.db = None
        self._connect_db()
        self._init_tables()
        self._load_default_config()

    def _connect_db(self) -> bool:
        """连接到 DuckDB 数据库

        Returns:
            是否成功连接
        """
        try:
            # 关闭现有连接
            if self.db:
                try:
                    self.db.close()
                except:
                    pass
                self.db = None

            # 确保目录存在
            db_dir = os.path.dirname(os.path.abspath(self.db_file))
            if db_dir and not os.path.exists(db_dir):
                os.makedirs(db_dir, exist_ok=True)

            # 检查文件是否存在且是否可能损坏
            if os.path.exists(self.db_file):
                try:
                    # 尝试连接数据库
                    self.db = duckdb.connect(self.db_file)
                    # 测试执行一个简单的SQL语句
                    self.db.execute("SELECT 1")
                except Exception as e:
                    log.warning(f"尝试连接到现有数据库失败，将删除并重建: {str(e)}")
                    # 如果无法正常连接，删除可能损坏的数据库文件
                    self.db = None
                    try:
                        os.remove(self.db_file)
                    except Exception as e2:
                        log.error(f"删除损坏的数据库文件失败: {str(e2)}")
                        return False

            # 连接数据库（如果之前没有成功连接）
            if not self.db:
                self.db = duckdb.connect(self.db_file)

            log.info(f"成功连接到配置数据库: {self.db_file}")
            return True
        except Exception as e:
            log.error(f"连接配置数据库失败: {str(e)}")
            self.db = None
            return False

    def _init_tables(self) -> bool:
        """初始化配置表

        Returns:
            是否成功初始化
        """
        if not self.db:
            return False

        try:
            # 创建配置表
            self.db.execute("""
                CREATE TABLE IF NOT EXISTS config (
                    key VARCHAR PRIMARY KEY,
                    value VARCHAR,
                    value_type VARCHAR NOT NULL
                )
            """)

            # 提交更改
            self.db.commit()
            log.info("成功初始化配置表")
            return True
        except Exception as e:
            log.error(f"初始化配置表失败: {str(e)}")
            return False

    def _load_default_config(self) -> None:
        """加载默认配置"""
        default_config = {
            # 基本设置
            'max_height_cm': (5000, 'int'),
            'ppi': (72, 'int'),
            'image_spacing_cm': (0.1, 'float'),
            'horizontal_expansion_cm': (0, 'float'),
            'last_library_path': ('', 'string'),
            'last_material_folder': ('', 'string'),
            'use_photoshop': (True, 'bool'),
            'auto_start_photoshop': (True, 'bool'),
            'save_format': ('TIFF', 'string'),
            'compression': ('LZW', 'string'),
            'exact_pattern_search': (False, 'bool'),  # 精确查询图案全称设置
            'is_standard_mode': (True, 'bool'),  # 表格模式设置，True为标准模式
            'is_fuzzy_query': (False, 'bool'),  # 模糊查询设置，True为开启模糊查询

            # 注意：已移除Tetris算法相关参数，现在使用RectPack算法

            # 测试模式参数
            'is_test_mode': (False, 'bool'),  # 是否开启测试模式
            'is_test_all_data': (False, 'bool'),  # 是否测试全部数据

            # 图库索引参数
            'is_db_scan_fast': (True, 'bool'),  # 是否使用快速模式索引图库

            # RectPack算法基础参数
            'use_rectpack_algorithm': (True, 'bool'),  # 是否使用RectPack算法（默认启用）替代传统算法
            'rectpack_rotation_enabled': (True, 'bool'),  # RectPack算法是否启用旋转
            'rectpack_sort_strategy': (0, 'int'),  # RectPack排序策略 (0=面积, 1=周长, 2=差值, 3=短边, 4=长边, 5=比例)
            'rectpack_pack_algorithm': (0, 'int'),  # RectPack装箱算法 (0=BNF, 1=BFF, 2=BBF)

            # RectPack算法高级参数
            'rectpack_bin_selection_strategy': (0, 'int'),  # Bin选择策略 (0=BestShortSideFit, 1=BestLongSideFit, 2=BestAreaFit, 3=BottomLeftRule, 4=ContactPointRule)
            'rectpack_split_heuristic': (0, 'int'),  # 分割启发式 (0=ShorterLeftoverHorizontal, 1=ShorterLeftoverVertical, 2=LongerLeftoverHorizontal, 3=LongerLeftoverVertical)
            'rectpack_free_rect_choice': (0, 'int'),  # 自由矩形选择 (0=BestShortSideFit, 1=BestLongSideFit, 2=BestAreaFit, 3=WorstAreaFit, 4=WorstShortSideFit, 5=WorstLongSideFit)

            # RectPack算法优化参数
            'rectpack_enable_optimization': (True, 'bool'),  # 是否启用利用率优化
            'rectpack_optimization_iterations': (5, 'int'),  # 优化迭代次数
            'rectpack_min_utilization_threshold': (85.0, 'float'),  # 最小利用率阈值（百分比）
            'rectpack_rotation_penalty': (0.05, 'float'),  # 旋转惩罚系数（0-1）
            'rectpack_aspect_ratio_preference': (1.0, 'float'),  # 宽高比偏好（1.0=无偏好，>1偏好横向，<1偏好纵向）

            # RectPack算法性能参数
            'rectpack_max_processing_time': (300, 'int'),  # 最大处理时间（秒）
            'rectpack_batch_size': (100, 'int'),  # 批处理大小
            'rectpack_memory_limit_mb': (1024, 'int'),  # 内存限制（MB）
            'rectpack_enable_parallel': (False, 'bool'),  # 是否启用并行处理

            # RectPack算法调试参数
            'rectpack_debug_mode': (False, 'bool'),  # 是否启用调试模式
            'rectpack_log_level': (1, 'int'),  # 日志级别 (0=无, 1=基础, 2=详细, 3=调试)
            'rectpack_save_intermediate_results': (False, 'bool'),  # 是否保存中间结果
            'rectpack_visualization_enabled': (False, 'bool')  # 是否启用可视化
        }

        # 确保所有默认值都存在
        for key, (value, value_type) in default_config.items():
            if not self._has_key(key):
                self.set(key, value)

        # 默认启用RectPack算法（已替换所有tetris算法）
        self._enable_rectpack_by_default()

    # 已移除tetris参数清理方法，因为已完全替换为RectPack算法

    def _enable_rectpack_by_default(self) -> None:
        """默认启用RectPack算法"""
        try:
            # 检查当前设置
            current_value = self.get('use_rectpack_algorithm', False)

            # 如果当前是禁用状态，则启用RectPack算法
            if not current_value:
                self.set('use_rectpack_algorithm', True)
                log.info("已默认启用RectPack算法")
            else:
                log.info("RectPack算法已启用")

        except Exception as e:
            log.error(f"启用RectPack算法失败: {str(e)}")

    def _has_key(self, key: str) -> bool:
        """检查配置键是否存在

        Args:
            key: 配置键

        Returns:
            是否存在
        """
        try:
            result = self.db.execute("SELECT COUNT(*) FROM config WHERE key = ?", [key]).fetchone()
            return result[0] > 0
        except Exception as e:
            log.error(f"检查配置键失败: {str(e)}")
            return False

    def _convert_value(self, value: Any, value_type: str) -> str:
        """将值转换为字符串表示

        Args:
            value: 值
            value_type: 值类型

        Returns:
            字符串表示
        """
        if value_type == 'bool':
            return str(int(value))
        return str(value)

    def _parse_value(self, value_str: str, value_type: str) -> Any:
        """将字符串解析为指定类型的值

        Args:
            value_str: 值字符串
            value_type: 值类型

        Returns:
            解析后的值
        """
        if value_type == 'int':
            return int(value_str)
        elif value_type == 'float':
            return float(value_str)
        elif value_type == 'bool':
            return bool(int(value_str))
        else:  # string
            return value_str

    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值

        Args:
            key: 配置键
            default: 默认值

        Returns:
            配置值
        """
        # 确保连接可用
        if not self.db:
            self._connect_db()

        if not self.db:  # 如果重连失败
            log.error(f"获取配置值 {key} 失败: 数据库连接不可用")
            return default

        try:
            result = self.db.execute(
                "SELECT value, value_type FROM config WHERE key = ?",
                [key]
            ).fetchone()

            if result:
                value_str, value_type = result
                return self._parse_value(value_str, value_type)
            return default
        except Exception as e:
            log.error(f"获取配置值失败: {str(e)}")
            # 尝试重新连接并再次获取
            if self._connect_db():
                try:
                    result = self.db.execute(
                        "SELECT value, value_type FROM config WHERE key = ?",
                        [key]
                    ).fetchone()

                    if result:
                        value_str, value_type = result
                        return self._parse_value(value_str, value_type)
                except Exception as e2:
                    log.error(f"重试获取配置值失败: {str(e2)}")
            return default

    def set(self, key: str, value: Any) -> bool:
        """设置配置值

        Args:
            key: 配置键
            value: 配置值

        Returns:
            是否成功
        """
        if value is None:
            return False

        # 确保连接可用
        if not self.db:
            self._connect_db()

        if not self.db:  # 如果重连失败
            log.error(f"设置配置值 {key} 失败: 数据库连接不可用")
            return False

        try:
            # 确定值类型
            if isinstance(value, bool):
                value_type = 'bool'
            elif isinstance(value, int):
                value_type = 'int'
            elif isinstance(value, float):
                value_type = 'float'
            else:
                value_type = 'string'

            # 转换值为字符串
            value_str = self._convert_value(value, value_type)

            # 插入或更新配置 (不使用事务，简化操作)
            self.db.execute("""
                INSERT OR REPLACE INTO config (key, value, value_type)
                VALUES (?, ?, ?)
            """, [key, value_str, value_type])

            return True
        except Exception as e:
            log.error(f"设置配置值失败: {str(e)}")
            # 尝试重新连接并再次设置
            if self._connect_db():
                try:
                    # 重新确定值类型
                    if isinstance(value, bool):
                        value_type = 'bool'
                    elif isinstance(value, int):
                        value_type = 'int'
                    elif isinstance(value, float):
                        value_type = 'float'
                    else:
                        value_type = 'string'

                    # 转换值为字符串
                    value_str = self._convert_value(value, value_type)

                    # 重新尝试插入或更新
                    self.db.execute("""
                        INSERT OR REPLACE INTO config (key, value, value_type)
                        VALUES (?, ?, ?)
                    """, [key, value_str, value_type])

                    return True
                except Exception as e2:
                    log.error(f"重试设置配置值失败: {str(e2)}")
            return False

    def update(self, updates: Dict[str, Any]) -> bool:
        """批量更新配置

        Args:
            updates: 要更新的配置字典

        Returns:
            是否全部成功
        """
        # 确保连接可用
        if not self.db:
            self._connect_db()

        if not self.db:  # 如果重连失败
            log.error("批量更新配置失败: 数据库连接不可用")
            return False

        success = True

        # 开始事务
        try:
            self.db.begin()

            for key, value in updates.items():
                # 确定值类型
                if isinstance(value, bool):
                    value_type = 'bool'
                elif isinstance(value, int):
                    value_type = 'int'
                elif isinstance(value, float):
                    value_type = 'float'
                else:
                    value_type = 'string'

                # 转换值为字符串
                value_str = self._convert_value(value, value_type)

                # 直接执行SQL而不调用set方法，避免嵌套事务
                self.db.execute("""
                    INSERT OR REPLACE INTO config (key, value, value_type)
                    VALUES (?, ?, ?)
                """, [key, value_str, value_type])

            # 提交事务
            self.db.commit()

        except Exception as e:
            # 回滚事务
            try:
                self.db.rollback()
            except:
                pass

            log.error(f"批量更新配置失败: {str(e)}")

            # 尝试重新连接
            if self._connect_db():
                try:
                    # 使用单独的更新而不是事务
                    for key, value in updates.items():
                        self.set(key, value)
                    success = True
                except Exception as e2:
                    log.error(f"重试批量更新配置失败: {str(e2)}")
                    success = False
            else:
                success = False

        return success

    def save_last_paths(self, library_path: Optional[str] = None, material_folder: Optional[str] = None) -> None:
        """保存最近使用的路径

        Args:
            library_path: 图片库路径
            material_folder: 材质文件夹路径
        """
        updates = {}
        if library_path is not None:
            updates['last_library_path'] = library_path
        if material_folder is not None:
            updates['last_material_folder'] = material_folder

        if updates:
            self.update(updates)

    def get_last_paths(self) -> Tuple[str, str]:
        """获取最近使用的路径

        Returns:
            (图片库路径, 材质文件夹路径)
        """
        return (
            self.get('last_library_path', ''),
            self.get('last_material_folder', '')
        )

    def sync_from_supabase(self) -> bool:
        """从 Supabase 同步配置

        Returns:
            是否成功同步
        """
        try:
            # 从云端获取配置，使用已认证的客户端（如果可用）
            supabase_config = self.supabase_helper.fetch_config()

            if not supabase_config:
                log.warning("未从云端获取到配置，跳过同步")
                return False

            # 映射云端配置与本地配置
            mapping = {
                'img_ppi': ('ppi', 'int'),
                'wide_add': ('horizontal_expansion_cm', 'float'),
                'img_gap': ('image_spacing_cm', 'float'),
                'max_height': ('max_height_cm', 'float'),
                'is_fullname_query': ('exact_pattern_search', 'bool'),  # 添加精确查询图案全称设置
                'is_standard_mode': ('is_standard_mode', 'bool'),  # 添加表格模式设置
                'is_fuzzy_query': ('is_fuzzy_query', 'bool'),  # 添加模糊查询设置

                # 注意：已移除Tetris算法相关参数映射

                # 添加测试模式参数映射
                'is_test_mode': ('is_test_mode', 'bool'),  # 是否开启测试模式
                'is_test_all_data': ('is_test_all_data', 'bool'),  # 是否测试全部数据

                # 添加图库索引参数映射
                'is_db_scan_fast': ('is_db_scan_fast', 'bool')  # 是否使用快速模式索引图库
            }

            updates = {}
            for cloud_key, (local_key, value_type) in mapping.items():
                if cloud_key in supabase_config and supabase_config[cloud_key] is not None:
                    value = supabase_config[cloud_key]

                    # 根据类型转换值
                    if value_type == 'int':
                        try:
                            value = int(value)
                            # 注意：已移除Tetris算法参数的特别记录
                        except (ValueError, TypeError) as e:
                            log.error(f"转换{cloud_key}为整数失败: {str(e)}")
                            continue
                    elif value_type == 'float':
                        try:
                            value = float(value)
                        except (ValueError, TypeError) as e:
                            log.error(f"转换{cloud_key}为浮点数失败: {str(e)}")
                            continue
                    elif value_type == 'bool':
                        # 确保布尔值正确转换
                        try:
                            if isinstance(value, bool):
                                pass  # 已经是布尔类型
                            elif isinstance(value, (int, float)):
                                value = bool(value)  # 0 -> False, 非0 -> True
                            elif isinstance(value, str):
                                value = value.lower() in ('true', 't', 'yes', 'y', '1')
                            else:
                                value = bool(value)  # 其他情况尝试转换
                        except Exception as e:
                            log.error(f"转换{cloud_key}为布尔值失败: {str(e)}")
                            continue

                    updates[local_key] = value

            # 注意：已移除Tetris算法参数的强制同步逻辑

            # 应用更新
            if updates:
                # 对每个值单独调用set()方法，避免使用事务的update()方法
                success = True
                for key, value in updates.items():
                    if not self.set(key, value):
                        success = False
                        log.error(f"更新配置项 {key} 失败")

                if success:
                    log.info(f"成功从云端同步了 {len(updates)} 个配置项")
                return success
            else:
                log.info("没有新的配置需要从 Supabase 同步")
                return True

        except Exception as e:
            log.error(f"从 Supabase 同步配置失败: {str(e)}")
            return False

    def get_canvas_settings(self) -> Dict[str, Any]:
        """获取画布设置

        Returns:
            画布设置字典
        """
        # 确保连接可用
        if not self.db:
            self._connect_db()

        # 先从 Supabase 同步最新配置
        self.sync_from_supabase()

        # 返回画布设置，使用安全的默认值以避免错误
        try:
            return {
                'max_height_cm': self.get('max_height_cm', 5000),
                'ppi': self.get('ppi', 72),
                'image_spacing_cm': self.get('image_spacing_cm', 0.1),
                'horizontal_expansion_cm': self.get('horizontal_expansion_cm', 0)
            }
        except Exception as e:
            log.error(f"获取画布设置失败: {str(e)}")
            # 返回默认设置
            return {
                'max_height_cm': 5000,
                'ppi': 72,
                'image_spacing_cm': 0.1,
                'horizontal_expansion_cm': 0
            }

    def get_photoshop_settings(self) -> Dict[str, Any]:
        """获取 Photoshop 设置

        Returns:
            Photoshop 设置字典
        """
        # 确保连接可用
        if not self.db:
            self._connect_db()

        try:
            return {
                'use_photoshop': self.get('use_photoshop', True),
                'auto_start_photoshop': self.get('auto_start_photoshop', True),
                'save_format': self.get('save_format', 'TIFF'),
                'compression': self.get('compression', 'LZW')
            }
        except Exception as e:
            log.error(f"获取Photoshop设置失败: {str(e)}")
            # 返回默认设置
            return {
                'use_photoshop': True,
                'auto_start_photoshop': True,
                'save_format': 'TIFF',
                'compression': 'LZW'
            }

    # 注意：已移除get_algorithm_settings方法，现在使用RectPack算法

    # 注意：已移除C类算法参数相关方法，现在使用RectPack算法

    def get_table_mode(self) -> bool:
        """获取表格模式设置

        Returns:
            是否为标准模式，True表示标准模式，False表示自定义模式
        """
        # 确保连接可用
        if not self.db:
            self._connect_db()

        # 返回表格模式设置
        try:
            return self.get('is_standard_mode', True)
        except Exception as e:
            log.error(f"获取表格模式设置失败: {str(e)}")
            # 返回默认值（标准模式）
            return True

    def sync_table_mode(self) -> bool:
        """专门同步表格模式设置

        Returns:
            是否成功同步
        """
        log.info("开始同步表格模式设置...")

        try:
            # 从云端获取配置
            supabase_config = self.supabase_helper.fetch_config()

            if not supabase_config:
                log.warning("未从云端获取到配置，跳过同步表格模式")
                return False

            # 检查是否有表格模式设置
            if 'is_standard_mode' in supabase_config and supabase_config['is_standard_mode'] is not None:
                try:
                    # 转换为布尔值
                    value = supabase_config['is_standard_mode']
                    if isinstance(value, bool):
                        is_standard_mode = value
                    elif isinstance(value, (int, float)):
                        is_standard_mode = bool(value)
                    elif isinstance(value, str):
                        is_standard_mode = value.lower() in ('true', 't', 'yes', 'y', '1')
                    else:
                        is_standard_mode = bool(value)

                    # 保存到本地配置
                    success = self.set('is_standard_mode', is_standard_mode)
                    if success:
                        log.info(f"成功同步表格模式设置: {'标准模式' if is_standard_mode else '自定义模式'}")
                    return success
                except Exception as e:
                    log.error(f"同步表格模式设置失败: {str(e)}")
                    return False
            else:
                log.info("云端配置中没有表格模式设置，使用默认值（标准模式）")
                return True

        except Exception as e:
            log.error(f"同步表格模式设置失败: {str(e)}")
            return False

    def get_fuzzy_query(self) -> bool:
        """获取模糊查询设置

        Returns:
            是否开启模糊查询，True表示开启模糊查询，False表示关闭模糊查询
        """
        # 确保连接可用
        if not self.db:
            self._connect_db()

        # 返回模糊查询设置
        try:
            return self.get('is_fuzzy_query', False)
        except Exception as e:
            log.error(f"获取模糊查询设置失败: {str(e)}")
            # 返回默认值（关闭模糊查询）
            return False

    def sync_fuzzy_query(self) -> bool:
        """专门同步模糊查询设置

        Returns:
            是否成功同步
        """
        log.info("开始同步模糊查询设置...")

        try:
            # 从云端获取配置
            supabase_config = self.supabase_helper.fetch_config()

            if not supabase_config:
                log.warning("未从云端获取到配置，跳过同步模糊查询设置")
                return False

            # 检查是否有模糊查询设置
            if 'is_fuzzy_query' in supabase_config and supabase_config['is_fuzzy_query'] is not None:
                try:
                    # 转换为布尔值
                    value = supabase_config['is_fuzzy_query']
                    if isinstance(value, bool):
                        is_fuzzy_query = value
                    elif isinstance(value, (int, float)):
                        is_fuzzy_query = bool(value)
                    elif isinstance(value, str):
                        is_fuzzy_query = value.lower() in ('true', 't', 'yes', 'y', '1')
                    else:
                        is_fuzzy_query = bool(value)

                    # 保存到本地配置
                    success = self.set('is_fuzzy_query', is_fuzzy_query)
                    if success:
                        log.info(f"成功同步模糊查询设置: {'开启' if is_fuzzy_query else '关闭'}")
                    return success
                except Exception as e:
                    log.error(f"同步模糊查询设置失败: {str(e)}")
                    return False
            else:
                log.info("云端配置中没有模糊查询设置，使用默认值（关闭模糊查询）")
                return True

        except Exception as e:
            log.error(f"同步模糊查询设置失败: {str(e)}")
            return False

    # 注意：已移除画布迭代参数相关方法，现在使用RectPack算法

    def get_test_mode_settings(self) -> Dict[str, Any]:
        """获取测试模式设置

        Returns:
            测试模式设置字典，包含is_test_mode和is_test_all_data
            注意：已移除miniature_ratio，现在使用cm直接转px的方式实现测试模式
        """
        # 确保连接可用
        if not self.db:
            self._connect_db()

        # 先从 Supabase 同步最新配置
        self.sync_from_supabase()

        # 返回测试模式设置，使用安全的默认值以避免错误
        try:
            return {
                'is_test_mode': self.get('is_test_mode', False),
                'is_test_all_data': self.get('is_test_all_data', False)
            }
        except Exception as e:
            log.error(f"获取测试模式设置失败: {str(e)}")
            # 返回默认设置
            return {
                'is_test_mode': False,
                'is_test_all_data': False
            }

    def get_db_scan_fast(self) -> bool:
        """获取图库索引快速模式设置

        Returns:
            是否使用快速模式索引图库，True表示使用快速模式，False表示使用完整模式
        """
        # 确保连接可用
        if not self.db:
            self._connect_db()

        # 返回图库索引快速模式设置
        try:
            return self.get('is_db_scan_fast', True)
        except Exception as e:
            log.error(f"获取图库索引快速模式设置失败: {str(e)}")
            # 返回默认值（使用快速模式）
            return True

    def sync_db_scan_fast(self) -> bool:
        """专门同步图库索引快速模式设置

        Returns:
            是否成功同步
        """
        log.info("开始同步图库索引快速模式设置...")

        try:
            # 从云端获取配置
            supabase_config = self.supabase_helper.fetch_config()

            if not supabase_config:
                log.warning("未从云端获取到配置，跳过同步图库索引快速模式设置")
                return False

            # 检查是否有图库索引快速模式设置
            if 'is_db_scan_fast' in supabase_config and supabase_config['is_db_scan_fast'] is not None:
                try:
                    # 转换为布尔值
                    value = supabase_config['is_db_scan_fast']
                    if isinstance(value, bool):
                        is_db_scan_fast = value
                    elif isinstance(value, (int, float)):
                        is_db_scan_fast = bool(value)
                    elif isinstance(value, str):
                        is_db_scan_fast = value.lower() in ('true', 't', 'yes', 'y', '1')
                    else:
                        is_db_scan_fast = bool(value)

                    # 保存到本地配置
                    success = self.set('is_db_scan_fast', is_db_scan_fast)
                    if success:
                        log.info(f"成功同步图库索引快速模式设置: {'开启' if is_db_scan_fast else '关闭'}")
                    return success
                except Exception as e:
                    log.error(f"同步图库索引快速模式设置失败: {str(e)}")
                    return False
            else:
                log.info("云端配置中没有图库索引快速模式设置，使用默认值（开启）")
                return True

        except Exception as e:
            log.error(f"同步图库索引快速模式设置失败: {str(e)}")
            return False

    def sync_test_mode_settings(self) -> bool:
        """专门同步测试模式设置

        Returns:
            是否成功同步
        """
        log.info("开始同步测试模式设置...")

        try:
            # 从云端获取配置
            supabase_config = self.supabase_helper.fetch_config()

            if not supabase_config:
                log.warning("未从云端获取到配置，跳过同步测试模式设置")
                return False

            # 检查是否有测试模式设置
            params = {
                'is_test_mode': ('is_test_mode', 'bool'),
                'is_test_all_data': ('is_test_all_data', 'bool')
            }

            updates = {}
            for cloud_key, (local_key, value_type) in params.items():
                if cloud_key in supabase_config and supabase_config[cloud_key] is not None:
                    try:
                        value = supabase_config[cloud_key]

                        # 根据类型转换值
                        if value_type == 'float':
                            value = float(value)
                        elif value_type == 'bool':
                            if isinstance(value, bool):
                                pass  # 已经是布尔类型
                            elif isinstance(value, (int, float)):
                                value = bool(value)  # 0 -> False, 非0 -> True
                            elif isinstance(value, str):
                                value = value.lower() in ('true', 't', 'yes', 'y', '1')
                            else:
                                value = bool(value)  # 其他情况尝试转换

                        # 保存到本地配置
                        success = self.set(local_key, value)
                        if success:
                            updates[local_key] = value
                            log.info(f"成功同步测试模式参数: {local_key} = {value}")
                    except Exception as e:
                        log.error(f"同步测试模式参数 {cloud_key} 失败: {str(e)}")

            if updates:
                log.info(f"成功同步了 {len(updates)} 个测试模式参数")
                return True
            else:
                log.info("云端配置中没有新的测试模式参数，使用默认值")
                return True

        except Exception as e:
            log.error(f"同步测试模式参数失败: {str(e)}")
            return False

    def get_rectpack_settings(self) -> Dict[str, Any]:
        """获取RectPack算法设置

        Returns:
            Dict[str, Any]: RectPack算法设置
        """
        return {
            # 基础参数
            'use_rectpack_algorithm': self.get('use_rectpack_algorithm', False),
            'rectpack_rotation_enabled': self.get('rectpack_rotation_enabled', True),
            'rectpack_sort_strategy': self.get('rectpack_sort_strategy', 0),
            'rectpack_pack_algorithm': self.get('rectpack_pack_algorithm', 0),

            # 高级参数
            'rectpack_bin_selection_strategy': self.get('rectpack_bin_selection_strategy', 0),
            'rectpack_split_heuristic': self.get('rectpack_split_heuristic', 0),
            'rectpack_free_rect_choice': self.get('rectpack_free_rect_choice', 0),

            # 优化参数
            'rectpack_enable_optimization': self.get('rectpack_enable_optimization', True),
            'rectpack_optimization_iterations': self.get('rectpack_optimization_iterations', 5),
            'rectpack_min_utilization_threshold': self.get('rectpack_min_utilization_threshold', 85.0),
            'rectpack_rotation_penalty': self.get('rectpack_rotation_penalty', 0.05),
            'rectpack_aspect_ratio_preference': self.get('rectpack_aspect_ratio_preference', 1.0),

            # 性能参数
            'rectpack_max_processing_time': self.get('rectpack_max_processing_time', 300),
            'rectpack_batch_size': self.get('rectpack_batch_size', 100),
            'rectpack_memory_limit_mb': self.get('rectpack_memory_limit_mb', 1024),
            'rectpack_enable_parallel': self.get('rectpack_enable_parallel', False),

            # 调试参数
            'rectpack_debug_mode': self.get('rectpack_debug_mode', False),
            'rectpack_log_level': self.get('rectpack_log_level', 1),
            'rectpack_save_intermediate_results': self.get('rectpack_save_intermediate_results', False),
            'rectpack_visualization_enabled': self.get('rectpack_visualization_enabled', False)
        }

    def set_rectpack_settings(self, settings: Dict[str, Any]) -> bool:
        """设置RectPack算法设置

        Args:
            settings: RectPack算法设置

        Returns:
            bool: 是否成功设置
        """
        valid_keys = {
            # 基础参数
            'use_rectpack_algorithm',
            'rectpack_rotation_enabled',
            'rectpack_sort_strategy',
            'rectpack_pack_algorithm',

            # 高级参数
            'rectpack_bin_selection_strategy',
            'rectpack_split_heuristic',
            'rectpack_free_rect_choice',

            # 优化参数
            'rectpack_enable_optimization',
            'rectpack_optimization_iterations',
            'rectpack_min_utilization_threshold',
            'rectpack_rotation_penalty',
            'rectpack_aspect_ratio_preference',

            # 性能参数
            'rectpack_max_processing_time',
            'rectpack_batch_size',
            'rectpack_memory_limit_mb',
            'rectpack_enable_parallel',

            # 调试参数
            'rectpack_debug_mode',
            'rectpack_log_level',
            'rectpack_save_intermediate_results',
            'rectpack_visualization_enabled'
        }

        # 过滤有效的设置
        filtered_settings = {k: v for k, v in settings.items() if k in valid_keys}

        if not filtered_settings:
            return False

        return self.update(filtered_settings)

    def sync_rectpack_settings(self) -> bool:
        """从 Supabase 同步 RectPack 算法设置

        Returns:
            bool: 是否成功同步
        """
        try:
            if not self.supabase_helper.is_authenticated():
                log.warning("用户未登录，无法同步RectPack算法设置")
                return False

            # 获取云端配置
            cloud_config = self.supabase_helper.get_user_config()
            if not cloud_config:
                log.info("云端没有RectPack算法配置，使用默认值")
                return True

            # 定义需要同步的RectPack参数
            rectpack_params = [
                # 基础参数
                'use_rectpack_algorithm', 'rectpack_rotation_enabled', 'rectpack_sort_strategy', 'rectpack_pack_algorithm',
                # 高级参数
                'rectpack_bin_selection_strategy', 'rectpack_split_heuristic', 'rectpack_free_rect_choice',
                # 优化参数
                'rectpack_enable_optimization', 'rectpack_optimization_iterations', 'rectpack_min_utilization_threshold',
                'rectpack_rotation_penalty', 'rectpack_aspect_ratio_preference',
                # 性能参数
                'rectpack_max_processing_time', 'rectpack_batch_size', 'rectpack_memory_limit_mb', 'rectpack_enable_parallel',
                # 调试参数
                'rectpack_debug_mode', 'rectpack_log_level', 'rectpack_save_intermediate_results', 'rectpack_visualization_enabled'
            ]

            updates = {}
            for param in rectpack_params:
                if param in cloud_config:
                    current_value = self.get(param)
                    cloud_value = cloud_config[param]

                    # 类型转换
                    if param in ['rectpack_min_utilization_threshold', 'rectpack_rotation_penalty', 'rectpack_aspect_ratio_preference']:
                        cloud_value = float(cloud_value)
                    elif param in ['use_rectpack_algorithm', 'rectpack_rotation_enabled', 'rectpack_enable_optimization',
                                 'rectpack_enable_parallel', 'rectpack_debug_mode', 'rectpack_save_intermediate_results',
                                 'rectpack_visualization_enabled']:
                        cloud_value = bool(cloud_value)
                    else:
                        cloud_value = int(cloud_value)

                    if current_value != cloud_value:
                        updates[param] = cloud_value

            if updates:
                success = self.update(updates)
                if success:
                    log.info(f"成功同步了 {len(updates)} 个RectPack算法参数")
                    return True
                else:
                    log.error("同步RectPack算法参数到本地数据库失败")
                    return False
            else:
                log.info("云端RectPack算法配置与本地一致，无需同步")
                return True

        except Exception as e:
            log.error(f"同步RectPack算法参数失败: {str(e)}")
            return False

    def close(self) -> None:
        """关闭数据库连接"""
        if self.db:
            try:
                self.db.close()
                self.db = None
            except Exception as e:
                log.error(f"关闭数据库连接失败: {str(e)}")